{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Accountant Dashboard{% endblock page_title %}

{% block extra_css %}

{% endblock extra_css %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-primary elevation-1">
            <i class="fas fa-users"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Total Staff</span>
            <span class="info-box-number">{{ total_staff }}</span>
            <div class="progress">
              <div class="progress-bar bg-primary" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list'%}" class="text-primary">View Details</a>
            </span>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-success elevation-1">
            <i class="fas fa-user-check"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Active Staff</span>
            <span class="info-box-number">{{ active_staff }}</span>
            <div class="progress">
              <div class="progress-bar bg-success" style="width: {% widthratio active_staff total_staff 100 %}%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list'%}?employment_type=Active" class="text-success">View Details</a>
            </span>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-info elevation-1">
            <i class="fas fa-user-tie"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Regular Staff</span>
            <span class="info-box-number">{{ regular_staff }}</span>
            <div class="progress">
              <div class="progress-bar bg-info" style="width: {% widthratio regular_staff total_staff 100 %}%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list' %}?employment_type=Regular" class="text-info">View Details</a>
            </span>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-warning elevation-1">
            <i class="fas fa-user-clock"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Contract Staff</span>
            <span class="info-box-number">{{ contrect_staff }}</span>
            <div class="progress">
              <div class="progress-bar bg-warning" style="width: {% widthratio contrect_staff total_staff 100 %}%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list' %}?employment_type=Contract" class="text-warning">View Details</a>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Welcome and Quick Actions Row -->
    <div class="row">
      <div class="col-md-8">
        <div class="card welcome-card">
          <div class="card-header border-0">
            <h3 class="card-title">
              <i class="fas fa-hand-wave mr-2"></i>
              Welcome back, {{ user.first_name }}!
            </h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <h5>Your Payroll Management Hub</h5>
                <p class="mb-3">Here's what you can accomplish today:</p>
                <ul class="list-unstyled">
                  <li><i class="fas fa-check-circle text-success mr-2"></i>Process payroll for all employees</li>
                  <li><i class="fas fa-check-circle text-success mr-2"></i>Generate and manage payslips</li>
                  <li><i class="fas fa-check-circle text-success mr-2"></i>Monitor financial records and reports</li>
                  <li><i class="fas fa-check-circle text-success mr-2"></i>Handle tax deductions and compliance</li>
                </ul>
              </div>
              <div class="col-md-4 text-center">
                <i class="fas fa-calculator fa-4x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-bolt mr-2"></i>
              Quick Actions
            </h3>
          </div>
          <div class="card-body quick-actions text-center">
            <a href="{% url 'generate_payslip_regular' %}" class="btn btn-primary">
              <i class="fas fa-calculator mr-2"></i>Generate Regular Payslips
            </a>
            <a href="{% url 'generate_payslip_contract' %}" class="btn btn-success">
              <i class="fas fa-file-contract mr-2"></i>Generate Contract Payslips
            </a>
            <a href="{% url 'generate_payslip_active' %}" class="btn btn-info">
              <i class="fas fa-user-plus mr-2"></i>Generate Active Payslips
            </a>
            <a href="{% url 'payroll_summary_report' %}" class="btn btn-warning">
              <i class="fas fa-chart-bar mr-2"></i>View Reports
            </a>
            <a href="{% url 'list_payslip' %}" class="btn btn-secondary">
              <i class="fas fa-file-invoice-dollar mr-2"></i>Manage Payslips
            </a>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <!-- Recent Activity -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-clock mr-2"></i>
              Recent Activity
            </h3>
          </div>
          <div class="card-body recent-activity">
            <div class="activity-item">
              <h6 class="mb-1">Payroll Generated</h6>
              <small class="text-muted">Regular staff payroll for current month</small>
              <br><small class="text-info">2 hours ago</small>
            </div>
            <div class="activity-item">
              <h6 class="mb-1">Staff Directory Updated</h6>
              <small class="text-muted">New employee records added</small>
              <br><small class="text-info">1 day ago</small>
            </div>
            <div class="activity-item">
              <h6 class="mb-1">Report Generated</h6>
              <small class="text-muted">Monthly payroll summary exported</small>
              <br><small class="text-info">2 days ago</small>
            </div>
            <div class="activity-item">
              <h6 class="mb-1">Deductions Updated</h6>
              <small class="text-muted">Tax deduction rates modified</small>
              <br><small class="text-info">3 days ago</small>
            </div>
          </div>
        </div>

        <!-- System Status -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-server mr-2"></i>
              System Status
            </h3>
          </div>
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span>Payroll System</span>
              <span class="badge badge-success">Online</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span>Database</span>
              <span class="badge badge-success">Connected</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span>Backup Status</span>
              <span class="badge badge-info">Up to date</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <span>Last Sync</span>
              <small class="text-muted">Just now</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock content %}
