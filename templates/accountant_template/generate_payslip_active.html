{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block body_class %}generate-payslip-page{% endblock body_class %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'plugins/bs-stepper/css/bs-stepper.min.css' %}">
{% endblock extra_css %}

{% block content %}
<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{{page_title}}</h3>
          </div>
          <div class="card-body p-0">
            <div class="bs-stepper">
              <div class="bs-stepper-header" role="tablist">
                <div class="step" data-target="#deductions-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="deductions-part"
                    id="deductions-part-trigger">
                    <span class="bs-stepper-circle">1</span>
                    <span class="bs-stepper-label">Deductions</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#paid-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="paid-part"
                    id="paid-part-trigger">
                    <span class="bs-stepper-circle">2</span>
                    <span class="bs-stepper-label">Pay Values</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#attendence-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="attendence-part"
                    id="attendence-part-trigger">
                    <span class="bs-stepper-circle">3</span>
                    <span class="bs-stepper-label">Attendance</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#generate-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="generate-part"
                    id="generate-part-trigger">
                    <span class="bs-stepper-circle">4</span>
                    <span class="bs-stepper-label">Generate</span>
                  </button>
                </div>
              </div>
              <div class="bs-stepper-content">
                <div id="deductions-part" class="content active bs-stepper-content" role="tabpanel"
                  aria-labelledby="deductions-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title"><i class="fas fa-minus-circle mr-2"></i>Enter Deduction Values</h3>
                          <div class="card-tools">
                            <small class="text-muted">Enter deduction amounts in rupees (₹)</small>
                          </div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                          <table id="example1" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                                <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                                <th><i class="fas fa-building mr-1"></i>Department</th>
                                <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                                <th><i class="fas fa-users mr-1"></i>Society (₹)</th>
                                <th><i class="fas fa-receipt mr-1"></i>Income Tax (₹)</th>
                                <th><i class="fas fa-utensils mr-1"></i>Canteen (₹)</th>
                                <th><i class="fas fa-hand-holding-usd mr-1"></i>Advance (₹)</th>
                                <th><i class="fas fa-shield-alt mr-1"></i>Insurance (₹)</th>
                                <th><i class="fas fa-ellipsis-h mr-1"></i>Miscellaneous (₹)</th>
                                <th><i class="fas fa-cog mr-1"></i>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form">
                                  {% csrf_token %}
                                  <td>{{ staff.staff.emp_code}}</td>
                                  <td>{{ staff.first_name }} {{ staff.last_name }}</td>
                                  <td>{{ staff.staff.department.name }}</td>
                                  <td>{{ staff.staff.designation.name }}</td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="society" id="society_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.society}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Society deduction amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="income_tax" id="income_tax_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.income_tax}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Income tax deduction amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="canteen" id="canteen_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.canteen}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Canteen deduction amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="advance" id="advance_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.advance}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Advance deduction amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="insurance" id="insurance_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.insurance}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Insurance deduction amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="other" id="other_id_{{ forloop.counter }}"
                                        value="{{staff.deductions.other}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Other miscellaneous deductions">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.staff.id}}">
                                    <button type="button" class="btn btn-success save-btn"><i
                                        class="fas fa-save mr-2"> Save</i></button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <!-- /.col -->
                  </div>
                  <!-- /.row -->
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="paid-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="paid-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title"><i class="fas fa-plus-circle mr-2"></i>Enter Pay Values</h3>
                          <div class="card-tools">
                            <small class="text-muted">Enter pay amounts in rupees (₹)</small>
                          </div>
                        </div>
                        <div class="card-body">
                          <table id="example2" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                                <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                                <th><i class="fas fa-building mr-1"></i>Department</th>
                                <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                                <th><i class="fas fa-calendar mr-1"></i>Month & Year</th>
                                <th><i class="fas fa-home mr-1"></i>HRA (₹)</th>
                                <th><i class="fas fa-money-bill-wave mr-1"></i>Adhoc (₹)</th>
                                <th><i class="fas fa-coins mr-1"></i>Arrears (₹)</th>
                                <th><i class="fas fa-plus mr-1"></i>Other Pay (₹)</th>
                                <th><i class="fas fa-cog mr-1"></i>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form">
                                  {% csrf_token %}
                                  <td>{{ staff.staff.emp_code}}</td>
                                  <td>{{ staff.first_name }} {{ staff.last_name }}</td>
                                  <td>{{ staff.staff.department.name }}</td>
                                  <td>{{ staff.staff.designation.name }}</td>
                                  <td>
                                    <input type="hidden" id="staff_id" name="staff_id" value="{{ staff.staff.id }}">
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                      </div>
                                      <input type="month" class="form-control monthpicker"
                                        id="monthpicker{{ staff.staff.id }}" name="month" required
                                        title="Select month and year for pay calculation">
                                    </div>
                                    <div class="invalid-feedback">Please select a valid month and year.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="hra" id="hra_id_{{ forloop.counter }}"
                                        value="{{staff.contract_pay.hra}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="House Rent Allowance amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="adhoc" id="adhoc_id_{{ forloop.counter }}"
                                        value="{{staff.contract_pay.adhoc}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Adhoc payment amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="arrears" id="arrears_id_{{ forloop.counter }}"
                                        value="{{staff.contract_pay.arrears}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Arrears amount">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <div class="input-group-prepend">
                                        <span class="input-group-text">₹</span>
                                      </div>
                                      <input type="number" name="other" id="other_id_{{ forloop.counter }}"
                                        value="{{staff.contract_pay.other}}" class="form-control" min="0" step="0.01"
                                        placeholder="0.00" title="Other additional pay">
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid amount.</div>
                                  </td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.staff.id}}">
                                    <button type="button" class="btn btn-success save-btn-pay"><i
                                        class="fas fa-save mr-2"></i> Save</button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>

                      </div>
                    </div>
                  </div>
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="attendence-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="attendence-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title"><i class="fas fa-calendar-check mr-2"></i>Enter Attendance Details</h3>
                          <div class="card-tools">
                            <small class="text-muted">Enter attendance information for payroll calculation</small>
                          </div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                          <table id="example3" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                                <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                                <th><i class="fas fa-building mr-1"></i>Department</th>
                                <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                                <th><i class="fas fa-calendar-day mr-1"></i>Paid Days</th>
                                <th><i class="fas fa-calendar-times mr-1"></i>Loss of Pay (LOP)</th>
                                <th><i class="fas fa-cog mr-1"></i>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form" method="post" action="{% url 'save_attendence_details' %}">
                                  {% csrf_token %}
                                  <td>{{ staff.staff.emp_code }}</td>
                                  <td>{{ staff.first_name }} {{ staff.last_name }}</td>
                                  <td>{{ staff.staff.department.name }}</td>
                                  <td>{{ staff.staff.designation.name }}</td>
                                  <td>
                                    <div class="input-group">
                                      <input type="number" name="paid_days" id="paid_days_id_{{ forloop.counter }}"
                                        value="{{ staff.attendance.paid_days }}" class="form-control" min="0" max="31"
                                        step="1" placeholder="30" title="Number of paid working days">
                                      <div class="input-group-append">
                                        <span class="input-group-text">days</span>
                                      </div>
                                    </div>
                                    <div class="invalid-feedback">Please enter valid paid days (0-31).</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <input type="number" name="lop" id="lop_id_{{ forloop.counter }}"
                                        value="{{ staff.attendance.lop }}" class="form-control" min="0" max="31"
                                        step="1" placeholder="0" title="Number of loss of pay days">
                                      <div class="input-group-append">
                                        <span class="input-group-text">days</span>
                                      </div>
                                    </div>
                                    <div class="invalid-feedback">Please enter valid LOP days (0-31).</div>
                                  </td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.staff.id}}">
                                    <button type="button" class="btn btn-success save-btn-attendence"><i
                                        class="fas fa-save mr-2"></i>Save</button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <!-- /.col -->
                  </div>
                  <!-- /.row -->
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="generate-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="generate-part-trigger">
                  <div class="row">
                    <div class="col-md-3"></div>
                    <div class="col-md-6">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title"><i class="fas fa-calculator mr-2"></i>Calculate Payroll</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                          <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>Ready to Calculate:</strong> Click the button below to generate payslips for all active employees based on the entered data.
                          </div>
                          <div class="text-center">
                            <button type="button" id="calculate-payroll-btn" class="btn btn-success btn-lg">
                              <i class="fas fa-calculator mr-2"></i> Calculate Payroll
                            </button>
                          </div>
                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <div class="col-md-3"></div>
                  </div>
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- /.row -->
</section>
{% endblock content %}

{% block extra_js %}
<!-- BS-Stepper -->
<script src="{% static 'plugins/bs-stepper/js/bs-stepper.min.js' %}"></script>
<script src="{% static 'plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>

<script>
  // Enhanced form validation functions
  function validateNumericInput(value, fieldName, min = 0, max = null) {
    const num = parseFloat(value);
    if (isNaN(num) || num < min) {
      toastr.error(`${fieldName} must be a valid number greater than or equal to ${min}`);
      return false;
    }
    if (max !== null && num > max) {
      toastr.error(`${fieldName} must be less than or equal to ${max}`);
      return false;
    }
    return true;
  }

  function validateAttendanceData(paidDays, lop) {
    const paid = parseInt(paidDays);
    const lopDays = parseInt(lop);

    if (isNaN(paid) || paid < 0 || paid > 31) {
      toastr.error('Paid days must be between 0 and 31');
      return false;
    }

    if (isNaN(lopDays) || lopDays < 0 || lopDays > 31) {
      toastr.error('LOP days must be between 0 and 31');
      return false;
    }

    if (paid + lopDays > 31) {
      toastr.error('Total of paid days and LOP days cannot exceed 31');
      return false;
    }

    return true;
  }

  // Enhanced form field validation on input
  function setupFieldValidation() {
    // Real-time validation for currency fields
    $('input[type="number"][name*="society"], input[type="number"][name*="income_tax"], input[type="number"][name*="canteen"], input[type="number"][name*="advance"], input[type="number"][name*="insurance"], input[type="number"][name*="other"], input[type="number"][name*="hra"], input[type="number"][name*="adhoc"], input[type="number"][name*="arrears"]').on('input', function() {
      const value = parseFloat($(this).val());
      const field = $(this);

      if (isNaN(value) || value < 0) {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });

    // Real-time validation for attendance fields
    $('input[name="paid_days"], input[name="lop"]').on('input', function() {
      const value = parseInt($(this).val());
      const field = $(this);

      if (isNaN(value) || value < 0 || value > 31) {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });

    // Real-time validation for month fields
    $('input[type="month"]').on('change', function() {
      const value = $(this).val();
      const field = $(this);

      if (!value || value.trim() === '') {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });
  }

  document.addEventListener('DOMContentLoaded', function () {
    window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    setupFieldValidation();

    const currentDate = new Date();
    const previousMonth = new Date(currentDate.setMonth(currentDate.getMonth() - 1));
    const previousMonthString = previousMonth.toISOString().split('T')[0].slice(0, 7);

    const monthPickers = document.querySelectorAll('.monthpicker');
    monthPickers.forEach(monthPicker => {
      monthPicker.value = previousMonthString;
    });
  });

  function getDaysInPreviousMonth() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const lastDayOfPreviousMonth = new Date(year, month, 0).getDate();
    return lastDayOfPreviousMonth;
  }

  const previousMonthDays = getDaysInPreviousMonth();

  document.querySelectorAll('input[name="paid_days"]').forEach(input => {
    if (!input.value) {
      input.value = previousMonthDays;
    }
  });

  document.querySelectorAll('input[name="lop"],input[name="arrears"],input[name="other"],input[name="hra"],input[name="adhoc"]').forEach(input => {
    if (!input.value) {
      input.value = 0;
    }
  });

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  const csrftoken = getCookie('csrftoken');

  $.ajaxSetup({
    beforeSend: function (xhr, settings) {
      if (!/^(GET|HEAD|OPTIONS|TRACE)$/.test(settings.type) && !this.crossDomain) {
        xhr.setRequestHeader("X-CSRFToken", csrftoken);
      }
    }
  });


  $(document).ready(function () {
    $("#example1").on("click", ".save-btn", function() {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_deduction_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example2").on("click", ".save-btn-pay", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_contract_pay" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example3").on("click", ".save-btn-attendence", function () {
      let form = $(this).closest('tr').find('form');
      let row = $(this).closest('tr');
      let paidDays = row.find('input[name="paid_days"]').val();
      let lop = row.find('input[name="lop"]').val();

      // Validate attendance data
      if (!validateAttendanceData(paidDays, lop)) {
        return;
      }

      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_attendence_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  document.getElementById('calculate-payroll-btn').addEventListener('click', function () {
    // Show confirmation dialog
    if (confirm('Are you sure you want to calculate payroll? This action will generate payslips for all active employees.')) {
      // Show loading state
      const button = this;
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Calculating...';
      button.disabled = true;

      const url = `{% url 'calculate_payroll_active' %}`;
      window.location.href = url;
    }
  });

</script>
{% endblock extra_js %}