{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block body_class %}generate-payslip-page{% endblock body_class %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'plugins/bs-stepper/css/bs-stepper.min.css' %}">

{% endblock extra_css %}

{% block content %}
<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{{page_title}}</h3>
          </div>
          <div class="card-body p-0">
            <div class="bs-stepper">
              <div class="bs-stepper-header" role="tablist">
                <div class="step" data-target="#month-year-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="month-year-part"
                    id="month-year-part-trigger">
                    <span class="bs-stepper-circle">1</span>
                    <span class="bs-stepper-label">Month & Year</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#deductions-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="deductions-part"
                    id="deductions-part-trigger">
                    <span class="bs-stepper-circle">2</span>
                    <span class="bs-stepper-label">Deductions</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#paid-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="paid-part"
                    id="paid-part-trigger">
                    <span class="bs-stepper-circle">3</span>
                    <span class="bs-stepper-label">Pad Values</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#attendence-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="attendence-part"
                    id="attendence-part-trigger">
                    <span class="bs-stepper-circle">4</span>
                    <span class="bs-stepper-label">Attendence</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#generate-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="generate-part"
                    id="generate-part-trigger">
                    <span class="bs-stepper-circle">5</span>
                    <span class="bs-stepper-label">Generate</span>
                  </button>
                </div>
              </div>

              <div id="month-year-part" class="content active bs-stepper-content" role="tabpanel"
                aria-labelledby="month-year-part-trigger">
                <div class="row">
                  <div class="col-md-8">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Enter Details</h3>
                      </div>
                      <div class="card-body">
                        <form id="calendar-form">
                          {% csrf_token %}
                          <div class="form-group">
                            <div class="alert alert-info">
                              <i class="fas fa-info-circle mr-2"></i>
                              <strong>Instructions:</strong> Review and update the allowance percentages below if needed. If no changes are required, proceed to the next step.
                            </div>
                          </div>
                          <div class="form-group">
                            {% if latest_entry %}
                            <table class="table table-bordered table-striped table-hover">
                              <thead class="thead-dark">
                                <tr>
                                  <th><i class="fas fa-building mr-1"></i>Division</th>
                                  <th><i class="fas fa-calendar mr-1"></i>Month & Year</th>
                                  <th><i class="fas fa-percentage mr-1"></i>DA (%)</th>
                                  <th><i class="fas fa-home mr-1"></i>HRA (%)</th>
                                  <th><i class="fas fa-cog mr-1"></i>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                {% for allowance in latest_entry %}
                                <tr>
                                  <td>
                                    <input type="text" class="form-control" name="division"
                                      value="{{ allowance.division }}" readonly>
                                  </td>
                                  <td>
                                    <div class="input-group date" id="monthpicker_{{ forloop.counter }}" data-target-input="nearest">
                                      <input type="month" class="form-control datetimepicker-input"
                                        data-target="#monthpicke_{{ forloop.counter }}r" name="month" id="month_{{ forloop.counter }}" />
                                      <div class="input-group-append" data-target="#monthpicker_{{ forloop.counter }}"
                                        data-toggle="datetimepicker">
                                        <div class="input-group-text"><i class="fa fa-calendar mr-2"></i></div>
                                      </div>
                                    </div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <input type="number" class="form-control" name="da" value="{{ allowance.da }}"
                                        min="0" max="100" step="0.01" title="Dearness Allowance percentage (0-100)">
                                      <div class="input-group-append">
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid DA percentage (0-100).</div>
                                  </td>
                                  <td>
                                    <div class="input-group">
                                      <input type="number" class="form-control" name="hra" value="{{ allowance.hra }}"
                                        min="0" max="100" step="0.01" title="House Rent Allowance percentage (0-100)">
                                      <div class="input-group-append">
                                        <span class="input-group-text">%</span>
                                      </div>
                                    </div>
                                    <div class="invalid-feedback">Please enter a valid HRA percentage (0-100).</div>
                                  </td>
                                  <td>
                                    <input type="hidden" name="fixed_id" value="{{ allowance.id }}">
                                    <button type="button" class="btn btn-success save-btn-fixed">
                                      <i class="fas fa-save mr-2"></i>Save
                                    </button>
                                  </td>
                                </tr>
                                {% endfor %}
                              </tbody>
                            </table>
                            {% else %}
                            <p>No fixed allowances found.</p>
                            {% endif %}
                          </div>
                        </form>
                      </div>

                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="card bg-gradient-warning">
                      <div class="card-header border-0">
                        <h3 class="card-title">
                          <i class="far fa-calendar-alt mr-2"></i>
                          Calendar
                        </h3>
                        <div class="card-tools">
                          <button type="button" class="btn btn-warning btn-sm" data-card-widget="collapse">
                            <i class="fas fa-minus mr-2"></i>
                          </button>
                          <button type="button" class="btn btn-warning btn-sm" data-card-widget="remove">
                            <i class="fas fa-times mr-2"></i>
                          </button>
                        </div>
                      </div>
                      <div class="card-body pt-0">
                        <div id="calendar" style="width: 100%; height: 300px;"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <button class="btn btn-primary" onclick="stepper.next()">Next</button>
              </div>

              <div id="deductions-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="deductions-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-minus-circle mr-2"></i>Enter Deduction Values</h3>
                        <div class="card-tools">
                          <small class="text-muted">Enter deduction amounts in rupees (₹)</small>
                        </div>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <div class="table-responsive">
                          <table id="example1" class="table table-bordered table-striped table-hover">
                          <thead class="thead-dark">
                            <tr>
                              <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                              <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                              <th><i class="fas fa-building mr-1"></i>Department</th>
                              <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                              <th><i class="fas fa-users mr-1"></i>Society (₹)</th>
                              <th><i class="fas fa-receipt mr-1"></i>Income Tax (₹)</th>
                              <th><i class="fas fa-utensils mr-1"></i>Canteen (₹)</th>
                              <th><i class="fas fa-hand-holding-usd mr-1"></i>Advance (₹)</th>
                              <th><i class="fas fa-shield-alt mr-1"></i>Insurance (₹)</th>
                              <th><i class="fas fa-ellipsis-h mr-1"></i>Miscellaneous (₹)</th>
                              <th><i class="fas fa-cog mr-1"></i>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form" method="post" action="{% url 'save_deduction_details' %}">
                                {% csrf_token %}
                                <td>{{ staff.emp_code}}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="society" id="society_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.society}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Society deduction amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="income_tax" id="income_tax_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.income_tax}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Income tax deduction amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="canteen" id="canteen_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.canteen}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Canteen deduction amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="advance" id="advance_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.advance}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Advance deduction amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="insurance" id="insurance_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.insurance}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Insurance deduction amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="other" id="other_id_{{ forloop.counter }}"
                                      value="{{staff.deductions.other}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Other miscellaneous deductions">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>

                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-deduction"><i
                                      class="fas fa-save mr-2"></i>Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                          </table>
                        </div>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                  </div>
                  <!-- /.col -->
                </div>
                <div class="d-flex justify-content-between">
                  <button class="btn btn-primary" onclick="stepper.previous()">
                    <i class="fas fa-arrow-left mr-2"></i>Previous
                  </button>
                  <button class="btn btn-primary" onclick="stepper.next()">
                    Next<i class="fas fa-arrow-right ml-2"></i>
                  </button>
                </div>
              </div>

              <div id="paid-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="paid-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-plus-circle mr-2"></i>Enter Pay Values</h3>
                        <div class="card-tools">
                          <small class="text-muted">Enter additional pay amounts in rupees (₹)</small>
                        </div>
                      </div>
                      <div class="card-body">
                        <div class="table-responsive">
                          <table id="example2" class="table table-bordered table-striped table-hover">
                          <thead class="thead-dark">
                            <tr>
                              <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                              <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                              <th><i class="fas fa-building mr-1"></i>Department</th>
                              <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                              <th><i class="fas fa-money-bill-wave mr-1"></i>Arrears (₹)</th>
                              <th><i class="fas fa-plus mr-1"></i>Other Pay (₹)</th>
                              <th><i class="fas fa-cog mr-1"></i>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form">
                                {% csrf_token %}
                                <td>{{ staff.emp_code }}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="arrears" id="arrears_id_{{ forloop.counter }}"
                                      value="{{staff.regular_pay.arrears}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Arrears amount">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <div class="input-group-prepend">
                                      <span class="input-group-text">₹</span>
                                    </div>
                                    <input type="number" name="other" id="other_id_{{ forloop.counter }}"
                                      value="{{staff.regular_pay.other}}" class="form-control" min="0" step="0.01"
                                      placeholder="0.00" title="Other additional pay">
                                  </div>
                                  <div class="invalid-feedback">Please enter a valid amount.</div>
                                </td>
                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-pay"><i
                                      class="fas fa-save mr-2"></i> Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                          </table>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
                <div class="d-flex justify-content-between">
                  <button class="btn btn-primary" onclick="stepper.previous()">
                    <i class="fas fa-arrow-left mr-2"></i>Previous
                  </button>
                  <button class="btn btn-primary" onclick="stepper.next()">
                    Next<i class="fas fa-arrow-right ml-2"></i>
                  </button>
                </div>
              </div>

              <div id="attendence-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="attendence-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-calendar-check mr-2"></i>Enter Attendance Details</h3>
                        <div class="card-tools">
                          <small class="text-muted">Enter attendance information for payroll calculation</small>
                        </div>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <div class="table-responsive">
                          <table id="example3" class="table table-bordered table-striped table-hover">
                          <thead class="thead-dark">
                            <tr>
                              <th><i class="fas fa-id-badge mr-1"></i>Emp Code</th>
                              <th><i class="fas fa-user mr-1"></i>Employee Name</th>
                              <th><i class="fas fa-building mr-1"></i>Department</th>
                              <th><i class="fas fa-briefcase mr-1"></i>Designation</th>
                              <th><i class="fas fa-calendar-day mr-1"></i>Paid Days</th>
                              <th><i class="fas fa-calendar-times mr-1"></i>Loss of Pay (LOP)</th>
                              <th><i class="fas fa-cog mr-1"></i>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form" method="post" action="{% url 'save_attendence_details' %}">
                                {% csrf_token %}
                                <td>{{ staff.emp_code }}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td>
                                  <div class="input-group">
                                    <input type="number" name="paid_days" id="paid_days_id_{{ forloop.counter }}"
                                      value="{{ staff.attendance.paid_days }}" class="form-control" min="0" max="31"
                                      step="1" placeholder="30" title="Number of paid working days">
                                    <div class="input-group-append">
                                      <span class="input-group-text">days</span>
                                    </div>
                                  </div>
                                  <div class="invalid-feedback">Please enter valid paid days (0-31).</div>
                                </td>
                                <td>
                                  <div class="input-group">
                                    <input type="number" name="lop" id="lop_id_{{ forloop.counter }}"
                                      value="{{ staff.attendance.lop }}" class="form-control" min="0" max="31"
                                      step="1" placeholder="0" title="Number of loss of pay days">
                                    <div class="input-group-append">
                                      <span class="input-group-text">days</span>
                                    </div>
                                  </div>
                                  <div class="invalid-feedback">Please enter valid LOP days (0-31).</div>
                                </td>
                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-attendence"><i
                                      class="fas fa-save mr-2"></i>Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                          </table>
                        </div>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                  </div>
                  <!-- /.col -->
                </div>
                <!-- /.row -->
                <div class="d-flex justify-content-between">
                  <button class="btn btn-primary" onclick="stepper.previous()">
                    <i class="fas fa-arrow-left mr-2"></i>Previous
                  </button>
                  <button class="btn btn-primary" onclick="stepper.next()">
                    Next<i class="fas fa-arrow-right ml-2"></i>
                  </button>
                </div>
              </div>

              <div id="generate-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="generate-part-trigger">
                <div class="row">
                  <div class="col-md-3"></div>
                  <div class="col-md-6">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-heartbeat mr-2"></i>Medical Allowance Configuration</h3>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <form id="medical-form">
                          <div class="form-group">
                            <div class="alert alert-info">
                              <i class="fas fa-info-circle mr-2"></i>
                              <strong>Note:</strong> Medical allowance is typically provided every 3 months. Select whether to include it in this month's payroll calculation.
                            </div>
                          </div>
                          <div class="form-group">
                            <label for="give_medical" class="font-weight-bold">
                              <i class="fas fa-stethoscope mr-2"></i>Include Medical Allowance This Month:
                            </label>
                            <select id="give_medical" name="give_medical" class="form-control select2bs4" required>
                              <option value="">-- Select Option --</option>
                              <option value="no" selected>No - Skip medical allowance</option>
                              <option value="yes">Yes - Include medical allowance</option>
                            </select>
                            <small class="form-text text-muted">Choose whether to include medical allowance in this month's payroll calculation.</small>
                          </div>
                          <div class="text-center">
                            <button type="button" id="calculate-payroll-btn" class="btn btn-success btn-lg">
                              <i class="fas fa-calculator mr-2"></i> Calculate Payroll
                            </button>
                          </div>
                        </form>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->

                  </div>
                  <div class="col-md-3"></div>
                </div>
                <div class="d-flex justify-content-start">
                  <button class="btn btn-primary" onclick="stepper.previous()">
                    <i class="fas fa-arrow-left mr-2"></i>Previous
                  </button>
                </div>
              </div>

            </div>
          </div>
          <!-- /.bs-stepper-content -->
        </div>
        <!-- /.bs-stepper -->
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
  </div>
  <!-- /.row -->
  </div>
  <!-- /.container-fluid -->
</section>
<!-- /.content -->
{% endblock content %}

{% block extra_js %}
<!-- BS-Stepper -->
<script src="{% static 'plugins/bs-stepper/js/bs-stepper.min.js' %}"></script>
<script src="{% static 'plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>

<script>
  // Enhanced form validation functions
  function validateFixedAllowanceData(data) {
    const errors = [];

    if (!data.month || data.month.trim() === '') {
      errors.push('Month is required');
    }

    const da = parseFloat(data.da);
    if (isNaN(da) || da < 0 || da > 100) {
      errors.push('DA percentage must be between 0 and 100');
    }

    const hra = parseFloat(data.hra);
    if (isNaN(hra) || hra < 0 || hra > 100) {
      errors.push('HRA percentage must be between 0 and 100');
    }

    if (errors.length > 0) {
      errors.forEach(error => toastr.error(error));
      return false;
    }

    return true;
  }

  function validateNumericInput(value, fieldName, min = 0, max = null) {
    const num = parseFloat(value);
    if (isNaN(num) || num < min) {
      toastr.error(`${fieldName} must be a valid number greater than or equal to ${min}`);
      return false;
    }
    if (max !== null && num > max) {
      toastr.error(`${fieldName} must be less than or equal to ${max}`);
      return false;
    }
    return true;
  }

  function validateAttendanceData(paidDays, lop) {
    const paid = parseInt(paidDays);
    const lopDays = parseInt(lop);

    if (isNaN(paid) || paid < 0 || paid > 31) {
      toastr.error('Paid days must be between 0 and 31');
      return false;
    }

    if (isNaN(lopDays) || lopDays < 0 || lopDays > 31) {
      toastr.error('LOP days must be between 0 and 31');
      return false;
    }

    if (paid + lopDays > 31) {
      toastr.error('Total of paid days and LOP days cannot exceed 31');
      return false;
    }

    return true;
  }

  // Enhanced form field validation on input
  function setupFieldValidation() {
    // Real-time validation for percentage fields
    $('input[name="da"], input[name="hra"]').on('input', function() {
      const value = parseFloat($(this).val());
      const field = $(this);

      if (isNaN(value) || value < 0 || value > 100) {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });

    // Real-time validation for currency fields
    $('input[type="number"][name*="society"], input[type="number"][name*="income_tax"], input[type="number"][name*="canteen"], input[type="number"][name*="advance"], input[type="number"][name*="insurance"], input[type="number"][name*="other"], input[type="number"][name*="arrears"]').on('input', function() {
      const value = parseFloat($(this).val());
      const field = $(this);

      if (isNaN(value) || value < 0) {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });

    // Real-time validation for attendance fields
    $('input[name="paid_days"], input[name="lop"]').on('input', function() {
      const value = parseInt($(this).val());
      const field = $(this);

      if (isNaN(value) || value < 0 || value > 31) {
        field.addClass('is-invalid');
      } else {
        field.removeClass('is-invalid').addClass('is-valid');
      }
    });
  }

  document.addEventListener('DOMContentLoaded', function () {
    window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    setupFieldValidation();
  });

  document.addEventListener('DOMContentLoaded', function () {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() === 0 ? 12 : today.getMonth()).toString().padStart(2, '0');
    const inputs = document.querySelectorAll('input[type="month"]'); // Select all month inputs
  inputs.forEach(input => {
    const id = input.id; // Get the ID of the current input
    if (id) {
      const inputYear = today.getMonth() === 0 ? year - 1 : year;
      input.value = `${inputYear}-${month}`;
    }
  });
  });

  function getDaysInPreviousMonth() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const lastDayOfPreviousMonth = new Date(year, month, 0).getDate();
    return lastDayOfPreviousMonth;
  }

  const previousMonthDays = getDaysInPreviousMonth();

  document.querySelectorAll('input[name="paid_days"]').forEach(input => {
    if (!input.value) {
      input.value = previousMonthDays;
    }
  });

  document.querySelectorAll('input[name="lop"],input[name="arrears"],input[name="other"]').forEach(input => {
    if (!input.value) {
      input.value = 0;
    }
  });

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  const csrftoken = getCookie('csrftoken');

  $.ajaxSetup({
    beforeSend: function (xhr, settings) {
      if (!/^(GET|HEAD|OPTIONS|TRACE)$/.test(settings.type) && !this.crossDomain) {
        xhr.setRequestHeader("X-CSRFToken", csrftoken);
      }
    }
  });


  $(function () {
    $('#monthpicker').datetimepicker({
      format: 'YYYY-MM',
      viewMode: 'months'
    });
  });

  $(document).ready(function () {
    $('#calendar-form').on("click", ".save-btn-fixed", function () {
      let row = $(this).closest('tr');
      let formData = {
        division: row.find('input[name="division"]').val(),
        month: row.find('input[name="month"]').val(),
        da: row.find('input[name="da"]').val(),
        hra: row.find('input[name="hra"]').val(),
        fixed_id: row.find('input[name="fixed_id"]').val()
      };

      let thisButton = $(this);

      // Validate form data
      if (!validateFixedAllowanceData(formData)) {
        return;
      }

      $.ajax({
        type: 'POST',
        url: '{% url "save_fixed" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message); // Use Toastr for success
          } else {
            if (typeof response.errors === 'object') { // Handle validation errors
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else { // Handle general error message
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          if (xhr.responseJSON && xhr.responseJSON.errors) {
            for (const field in xhr.responseJSON.errors) {
              toastr.error(xhr.responseJSON.errors[field]);
            }
          } else {
            toastr.error("An unexpected error occurred. Please try again.");
          }
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example1").on("click", ".save-btn-deduction", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_deduction_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example2").on("click", ".save-btn-pay", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_regular_pay" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        },
      });
    });
  });


  $(document).ready(function () {
    $("#example3").on("click", ".save-btn-attendence", function () {
      let form = $(this).closest('tr').find('form');
      let row = $(this).closest('tr');
      let paidDays = row.find('input[name="paid_days"]').val();
      let lop = row.find('input[name="lop"]').val();

      // Validate attendance data
      if (!validateAttendanceData(paidDays, lop)) {
        return;
      }

      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_attendence_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });


  document.getElementById('calculate-payroll-btn').addEventListener('click', function () {
    const giveMedical = document.getElementById('give_medical').value;

    // Validate medical selection
    if (!giveMedical || giveMedical.trim() === '') {
      toastr.error('Please select whether to include medical allowance');
      document.getElementById('give_medical').focus();
      return;
    }

    // Show confirmation dialog
    const medicalText = giveMedical === 'yes' ? 'with medical allowance' : 'without medical allowance';
    if (confirm(`Are you sure you want to calculate payroll ${medicalText}? This action will generate payslips for all regular employees.`)) {
      // Show loading state
      const button = this;
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Calculating...';
      button.disabled = true;

      const url = `{% url 'calculate_payroll_regular' %}?give_medical=${giveMedical}`;
      window.location.href = url;
    }
  });

  $(document).ready(function () {
    $('#calendar').datetimepicker({
      format: 'L',
      inline: true
    });
  });

</script>
{% endblock extra_js %}