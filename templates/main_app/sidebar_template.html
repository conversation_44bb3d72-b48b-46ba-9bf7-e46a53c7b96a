{% load static %}
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="{% if request.user.user_type == 1 %}{% url 'admin_home' %}{% elif request.user.user_type == 2 %}{% url 'accountant_home' %}{% else %}{% url 'staff_home' %}{% endif %}" class="brand-link">
        <img src="{% static 'img/ntc.png' %}" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">
            {% if request.user.user_type == 1 %}
            <strong>Admin</strong> Panel
            {% elif request.user.user_type == 2 %}
            <strong>Accountant</strong> Panel
            {% else %}
            <strong>Staff</strong> Panel
            {% endif %}
        </span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <div class="image">
                {% if request.user.profile_pic %}
                <img src="{{ request.user.profile_pic }}" class="img-circle elevation-2" alt="User Image" style="width: 2.1rem; height: 2.1rem;">
                {% else %}
                <img src="{% static 'dist/img/default-avatar.jpeg' %}" class="img-circle elevation-2" alt="User Image" style="width: 2.1rem; height: 2.1rem;">
                {% endif %}
            </div>
            <div class="info">
                <a href="{% if user.user_type == 1 %}{% url 'admin_view_profile' %}{% elif user.user_type == 2 %}{% url 'accountant_view_profile' %}{% else %}{% url 'staff_view_profile' %}{% endif %}" class="d-block">
                    {% if user.first_name or user.last_name %}
                        {{ user.first_name }} {{ user.last_name }}
                    {% else %}
                        {{ user.email }}
                    {% endif %}
                </a>
                <small class="text-muted">
                    {% if request.user.user_type == 1 %}
                    System Administrator
                    {% elif request.user.user_type == 2 %}
                    Payroll Accountant
                    {% else %}
                    Staff Member
                    {% endif %}
                </small>
            </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                <!-- Add icons to the links using the .nav-icon class with font-awesome or any other icon font library -->

                {% if request.user.user_type == 1 %}
                <!-- ADMIN NAVIGATION -->
                <li class="nav-header">MAIN NAVIGATION</li>

                <li class="nav-item">
                    {% url 'admin_home' as admin_home %}
                    <a href="{{admin_home}}" class="nav-link {% if admin_home == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Dashboard</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'admin_view_profile' as admin_view_profile %}
                    <a href="{{admin_view_profile}}" class="nav-link {% if admin_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-user-cog"></i>
                        <p>My Profile</p>
                    </a>
                </li>

                <li class="nav-header">ORGANIZATION SETUP</li>

                <li class="nav-item">
                    {% url 'division' as manage_division %}
                    <a href="{{manage_division}}" class="nav-link {% if manage_division == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-sitemap"></i>
                        <p>Divisions</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'department' as manage_department %}
                    <a href="{{manage_department}}" class="nav-link {% if manage_department == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-building"></i>
                        <p>Departments</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'designation' as manage_designation %}
                    <a href="{{manage_designation}}" class="nav-link {% if edit_designation == request.path or manage_designation == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-id-badge"></i>
                        <p>Designations</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'grade' as manage_grade %}
                    <a href="{{manage_grade}}" class="nav-link {% if manage_grade == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-layer-group"></i>
                        <p>Grades</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'fixed' as manage_fixed %}
                    <a href="{{manage_fixed}}" class="nav-link {% if manage_fixed == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-percentage"></i>
                        <p>Fixed Allowances</p>
                    </a>
                </li>

                <li class="nav-header">USER MANAGEMENT</li>

                <li class="nav-item">
                    {% url 'manage_accountant' as manage_accountant %}
                    <a href="{{manage_accountant}}" class="nav-link {% if manage_accountant == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-user-tie"></i>
                        <p>Accountants</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'manage_staff' as manage_staff %}
                    <a href="{{manage_staff}}" class="nav-link {% if manage_staff == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-users"></i>
                        <p>Staff Members</p>
                    </a>
                </li>

                <li class="nav-header">PAYROLL MANAGEMENT</li>

                <li class="nav-item">
                    {% url 'list_payslip' as list_payslip %}
                    <a href="{{list_payslip}}" class="nav-link {% if list_payslip == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-invoice-dollar"></i>
                        <p>Generated Payslips</p>
                    </a>
                </li>

                <li class="nav-item {% if list_payslip_regular == request.path or list_payslip_contract == request.path or list_payslip_active == request.path %}menu-open{% endif %}">
                    {% url 'list_payslip_type' 'Regular' as list_payslip_regular %}
                    {% url 'list_payslip_type' 'Contract' as list_payslip_contract %}
                    {% url 'list_payslip_type' 'Active' as list_payslip_active %}

                    <a href="#" class="nav-link {% if list_payslip_regular == request.path or list_payslip_contract == request.path or list_payslip_active == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-edit"></i>
                        <p>
                            Edit Payslips
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="{{list_payslip_regular}}" class="nav-link {% if list_payslip_regular == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Regular Staff</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{list_payslip_contract}}" class="nav-link {% if list_payslip_contract == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Contract Staff</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{list_payslip_active}}" class="nav-link {% if list_payslip_active == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Active Staff</p>
                            </a>
                        </li>
                    </ul>
                </li>



                {% elif request.user.user_type == 2 %}
                <!-- ACCOUNTANT NAVIGATION -->
                <li class="nav-header">MAIN NAVIGATION</li>

                <li class="nav-item">
                    {% url 'accountant_home' as accountant_home %}
                    <a href="{{accountant_home}}" class="nav-link {% if accountant_home == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Dashboard</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'accountant_view_profile' as accountant_view_profile %}
                    <a href="{{accountant_view_profile}}" class="nav-link {% if accountant_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-user-cog"></i>
                        <p>My Profile</p>
                    </a>
                </li>

                <li class="nav-header">PAYROLL GENERATION</li>

                <li class="nav-item {% if generate_payslip_regular == request.path or generate_payslip_contract == request.path or generate_payslip_active == request.path %}menu-open{% endif %}">
                    {% url 'generate_payslip_regular' as generate_payslip_regular %}
                    {% url 'generate_payslip_contract' as generate_payslip_contract %}
                    {% url 'generate_payslip_active' as generate_payslip_active %}

                    <a href="#" class="nav-link {% if generate_payslip_regular == request.path or generate_payslip_contract == request.path or generate_payslip_active == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-calculator"></i>
                        <p>
                            Generate Payslips
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">

                        <li class="nav-item">
                            <a href="{{generate_payslip_regular}}" class="nav-link {% if generate_payslip_regular == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Regular Staff</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{generate_payslip_contract}}" class="nav-link {% if generate_payslip_contract == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Contract Staff</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{generate_payslip_active}}" class="nav-link {% if generate_payslip_active == request.path %} active {% endif %}">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Active Staff</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-header">PAYROLL MANAGEMENT</li>

                <li class="nav-item">
                    {% url 'list_payslip' as list_payslip %}
                    <a href="{{list_payslip}}" class="nav-link {% if list_payslip == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-invoice-dollar"></i>
                        <p>Generated Payslips</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'staff_list' as staff_list %}
                    <a href="{{staff_list}}" class="nav-link {% if staff_list == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-users"></i>
                        <p>Staff Directory</p>
                    </a>
                </li>

                <li class="nav-header">REPORTS</li>

                <li class="nav-item">
                    {% url 'payroll_summary_report' as payroll_summary_report %}
                    <a href="{{payroll_summary_report}}" class="nav-link {% if payroll_summary_report == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <p>Summary Report</p>
                    </a>
                </li>


                {% elif user.user_type == 3 %}
                <!-- STAFF NAVIGATION -->
                <li class="nav-header">MAIN NAVIGATION</li>

                <li class="nav-item">
                    {% url 'staff_home' as staff_home %}
                    <a href="{{staff_home}}" class="nav-link {% if staff_home == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Dashboard</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'staff_view_profile' as staff_view_profile %}
                    <a href="{{staff_view_profile}}" class="nav-link {% if staff_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-user-cog"></i>
                        <p>My Profile</p>
                    </a>
                </li>

                <li class="nav-header">PAYROLL</li>

                <li class="nav-item">
                    {% url 'staff_list_payslips' as staff_list_payslips %}
                    <a href="{{staff_list_payslips}}" class="nav-link {% if staff_list_payslips == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-invoice"></i>
                        <p>My Payslips</p>
                    </a>
                </li>

                {% endif %}


                <!-- LOGOUT SECTION -->
                {% if user.is_authenticated %}
                <li class="nav-header">ACCOUNT</li>
                <li class="nav-item">
                    <a href="{% url 'user_logout' %}" class="nav-link" data-confirm="Your session would be terminated.\n\nProceed?">
                        <i class="nav-icon fas fa-sign-out-alt text-danger"></i>
                        <p class="text">Logout</p>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>