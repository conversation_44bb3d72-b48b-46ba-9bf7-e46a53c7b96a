{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Payroll Management System{% endblock title %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/ntc.png' %}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'plugins/fontawesome-free/css/all.min.css' %}">
    <!-- AdminLTE Theme style -->
    <link rel="stylesheet" href="{% static 'dist/css/adminlte.min.css' %}">
    <!-- Toastr -->
    <link rel="stylesheet" href="{% static 'plugins/toastr/toastr.min.css' %}">
    <!-- DataTables -->
    <link rel="stylesheet" href="{% static 'plugins/datatables-bs4/css/dataTables.bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/datatables-responsive/css/responsive.bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/datatables-buttons/css/buttons.bootstrap4.min.css' %}">
    <!-- Select2 -->
    <link rel="stylesheet" href="{% static 'plugins/select2/css/select2.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Google Font: Source Sans Pro -->
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        .content-wrapper {
            background-color: #f4f6f9;
        }

        .main-header {
            border-bottom: 1px solid #dee2e6;
        }

        .brand-link {
            border-bottom: 1px solid #4b545c;
        }

        .card {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            margin-bottom: 1rem;
        }

        .small-box {
            border-radius: 0.25rem;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        }

        .navbar-nav .nav-link {
            color: #495057 !important;
        }

        .navbar-nav .nav-link:hover {
            color: #007bff !important;
        }

        .btn {
            border-radius: 0.25rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
        }

        .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
            background-color: #007bff;
            color: #fff;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: none;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>

    {% block extra_css %}{% endblock extra_css %}
    <script src="{% static 'plugins/jquery/jquery.min.js' %}"></script>
</head>

<body class="hold-transition sidebar-mini layout-fixed {% block body_class %}{% endblock body_class %}">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    </div>

    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                        <i class="fas fa-bars"></i>
                    </a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{% url 'admin_home' %}" class="nav-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <!-- User Account Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#" role="button">
                        <i class="far fa-user"></i>
                        <span class="d-none d-md-inline">
                            {% if user.first_name %}
                                {{ user.first_name }}
                            {% else %}
                                {{ user.email|truncatechars:15 }}
                            {% endif %}
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                        <span class="dropdown-item dropdown-header">
                            {% if user.first_name or user.last_name %}
                                {{ user.first_name }} {{ user.last_name }}
                            {% else %}
                                {{ user.email }}
                            {% endif %}
                        </span>
                        <div class="dropdown-divider"></div>
                        <a href="{% if user.user_type == 1 %}{% url 'admin_view_profile' %}{% elif user.user_type == 2 %}{% url 'accountant_view_profile' %}{% else %}{% url 'staff_view_profile' %}{% endif %}" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> Profile
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{% url 'user_logout' %}" class="dropdown-item" onclick="return confirm('Your session would be terminated.\n\nProceed?')">
                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                        </a>
                    </div>
                </li>
                <!-- Fullscreen Toggle -->
                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>
            </ul>
        </nav>

        {% include "main_app/sidebar_template.html" with user=user %}

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header (Page header) -->
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">{% block page_title %}{% endblock page_title %}</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item">
                                    <a href="{% if user.user_type == 1 %}{% url 'admin_home' %}{% elif user.user_type == 2 %}{% url 'accountant_home' %}{% else %}{% url 'staff_home' %}{% endif %}">
                                        <i class="fas fa-home"></i> Home
                                    </a>
                                </li>
                                {% block breadcrumb %}
                                <li class="breadcrumb-item active">{% block page_title_breadcrumb %}{{ page_title }}{% endblock %}</li>
                                {% endblock breadcrumb %}
                            </ol>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    {% block content %}
                    {% endblock content %}
                </div>
            </section>
        </div>

        <!-- Footer -->
        {% include "main_app/footer.html" %}

        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <!-- Control sidebar content goes here -->
        </aside>
    </div>

    <script src="{% static 'plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'dist/js/adminlte.min.js' %}"></script>
    <script src="{% static 'plugins/toastr/toastr.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-bs4/js/dataTables.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-responsive/js/dataTables.responsive.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-responsive/js/responsive.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/dataTables.buttons.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/jszip/jszip.min.js' %}"></script>
    <script src="{% static 'plugins/pdfmake/pdfmake.min.js' %}"></script>
    <script src="{% static 'plugins/pdfmake/vfs_fonts.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.html5.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.print.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.colVis.min.js' %}"></script>
    <script src="{% static 'plugins/select2/js/select2.full.min.js' %}"></script>

    <!-- Enhanced JavaScript -->
    <script>
        $(document).ready(function () {
            // Enhanced Toastr configuration
            toastr.options = {
                closeButton: true,
                debug: false,
                newestOnTop: true,
                progressBar: true,
                positionClass: "toast-top-right",
                preventDuplicates: false,
                onclick: null,
                showDuration: "300",
                hideDuration: "1000",
                timeOut: "5000",
                extendedTimeOut: "1000",
                showEasing: "swing",
                hideEasing: "linear",
                showMethod: "fadeIn",
                hideMethod: "fadeOut"
            };

            // Display Django messages
            {% if messages %}
            {% for message in messages %}
            toastr["{{ message.tags }}"]("{{ message|escapejs }}");
            {% endfor %}
            {% endif %}

            // Enhanced Select2 initialization
            $('.select2bs4').select2({
                theme: 'bootstrap4',
                width: '100%'
            });

            // Loading overlay functions
            window.showLoading = function() {
                $('#loadingOverlay').fadeIn();
            };

            window.hideLoading = function() {
                $('#loadingOverlay').fadeOut();
            };

            // Form submission loading
            $('form').on('submit', function() {
                showLoading();
            });

            // AJAX setup for loading overlay
            $(document).ajaxStart(function() {
                showLoading();
            }).ajaxStop(function() {
                hideLoading();
            });

            // Enhanced DataTables configuration
            if ($.fn.DataTable) {
                // Check if we're on a Generate Payslips page
                var isGeneratePayslipPage = $('body').hasClass('generate-payslip-page');

                // Base configuration for all tables
                var baseConfig = {
                    "responsive": true,
                    "lengthChange": true,
                    "autoWidth": false,
                    "processing": true,
                    "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                    "language": {
                        "search": "Search:",
                        "lengthMenu": "Show _MENU_ entries",
                        "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                        "infoEmpty": "Showing 0 to 0 of 0 entries",
                        "infoFiltered": "(filtered from _MAX_ total entries)",
                        "paginate": {
                            "first": "First",
                            "last": "Last",
                            "next": "Next",
                            "previous": "Previous"
                        }
                    }
                };

                // Add buttons configuration only if NOT on Generate Payslips page
                if (!isGeneratePayslipPage) {
                    baseConfig.buttons = [
                        {
                            extend: 'excel',
                            className: 'btn btn-success btn-sm'
                        },
                        {
                            extend: 'pdf',
                            className: 'btn btn-danger btn-sm'
                        },
                        {
                            extend: 'print',
                            className: 'btn btn-info btn-sm'
                        },
                        {
                            extend: 'colvis',
                            className: 'btn btn-secondary btn-sm'
                        }
                    ];
                }

                // Initialize DataTables
                var tables = $("#example1, #example2, #example3").DataTable(baseConfig);

                // Append buttons container only if buttons are configured
                if (!isGeneratePayslipPage && tables.buttons) {
                    tables.buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0), #example2_wrapper .col-md-6:eq(0), #example3_wrapper .col-md-6:eq(0)');
                }
            }

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var href = this.getAttribute('href');
                // Only proceed if href is more than just '#' and is a valid selector
                if (href && href.length > 1 && href !== '#') {
                    try {
                        var target = $(href);
                        if( target.length ) {
                            event.preventDefault();
                            $('html, body').stop().animate({
                                scrollTop: target.offset().top - 100
                            }, 1000);
                        }
                    } catch (e) {
                        // Ignore invalid selectors
                        console.warn('Invalid selector:', href);
                    }
                }
            });

            // Confirm dialogs enhancement
            $('[data-confirm]').on('click', function(e) {
                if (!confirm($(this).data('confirm'))) {
                    e.preventDefault();
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock extra_js %}

</body>

</html>