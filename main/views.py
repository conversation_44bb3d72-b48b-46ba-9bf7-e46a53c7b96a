from calendar import month_name, monthrange
from datetime import datetime, timedelta
from django.db.models import Prefetch
from weasyprint import HTML, CSS
from django.template.loader import render_to_string
from smtplib import SMTPException
from django.core.mail import EmailMessage
import json
from django.http import BadHeaderError
from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render, reverse
from django.contrib.auth import login, logout
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required

from PMS import settings
from main.models import Attendance, ContractPay, Deductions, Division, Fixed, Payslip, RegularPay, Staff
from .forms import LoginForm
from .backends.EmailBackend import EmailBackend

import json
import requests

def login_page(request):
    """
    Display the login page and handle login form submission.
    """
    if request.user.is_authenticated:
        return redirect_to_correct_home(request)

    if request.method == 'POST':
        return doLogin(request)

    form = LoginForm()
    context = {
        'form': form,
        'GOOGLE_RECAPTCHA_SITE_KEY': settings.GOOGLE_RECAPTCHA_SITE_KEY
    }
    return render(request, 'main_app/login.html', context)

@require_POST
def doLogin(request):
    """
    Handle login form submission and perform reCAPTCHA verification.
    """
    captcha_token = request.POST.get('g-recaptcha-response')
    captcha_url = "https://www.google.com/recaptcha/api/siteverify"
    captcha_key = settings.GOOGLE_RECAPTCHA_SECRET_KEY
    data = {
        'secret': captcha_key,
        'response': captcha_token
    }

    try:
        captcha_server = requests.post(url=captcha_url, data=data)
        response = json.loads(captcha_server.text)

        if not response.get('success'):
            messages.error(request, 'Invalid Captcha. Try Again')
            return redirect('login_page')
    except requests.RequestException as e:
        messages.error(request, f'Captcha could not be verified. Try Again. Error: {e}')
        return redirect('login_page')

    form = LoginForm(request.POST)
    if form.is_valid():
        user = EmailBackend.authenticate(request, username=form.cleaned_data['email'], password=form.cleaned_data['password'])

        if user is not None:
            if not user.is_active:
                messages.error(request, "Your account is inactive. Please contact the administrator.")
                return redirect("login_page")
            login(request, user)
            messages.success(request, "Login successful!")
            return redirect_to_correct_home(request)
        else:
            messages.error(request, "Invalid credentials. Please try again.")
            return redirect("login_page")
    else:
        messages.error(request, "Form is not valid. Please check the inputs.")
        return redirect("login_page")

def redirect_to_correct_home(request):
    """
    Redirect authenticated users to their appropriate home page based on user type.
    """
    user_type_redirects = {
        1: "admin_home",
        2: "accountant_home",
        3: "staff_home"
    }
    return redirect(reverse(user_type_redirects.get(request.user.user_type, 'login_page')))

def logout_user(request):
    """
    Log out the user and redirect to the login page.
    """
    if request.user.is_authenticated:
        logout(request)
        messages.success(request, "Logout successful!")
    return redirect("login_page")

def view_payslip(request, staff_id, month):
    """
    View the payslip for a given staff member and month.
    """
    staff = get_object_or_404(Staff, id=staff_id)
    payslip = Payslip.objects.filter(staff=staff, month=month).first()

    if not payslip:
        return JsonResponse({'error': 'Payslip not found'}, status=404)

    month_year = payslip.month
    monthNum = month_year.month
    monthName = month_name[monthNum]
    monthNumy = month_year.year
    num_days_in_month = monthrange(month_year.year, month_year.month)[1]

    logo = "nitra" if staff.division.name == "NITRA" else "ntc"

    payroll_info = {
        'employee_code': staff.emp_code,
        'employee_name': staff.user.get_full_name(),
        'designation': staff.designation.name,
        'department': staff.department.name,
        'father_husband_name': staff.user.father_name,
        'uan_pf_ac': staff.uan,
        'basic': payslip.basic,
        'doj': staff.emp_doj,
        'paid_days': payslip.paid_days,
        'lop': payslip.lop,
        'leave': num_days_in_month - payslip.paid_days + payslip.lop,
        'dp': payslip.dp,
        'basic_dp': payslip.basic + payslip.dp,
        'da': payslip.da,
        'basic_dp_da': payslip.basic + payslip.da + payslip.dp,
        'hra': payslip.hra,
        'conv': payslip.conv,
        'cca': payslip.cca,
        'adhoc': payslip.adhoc,
        'medical': payslip.medical,
        'arrears': payslip.arrears,
        'gross_pay': payslip.gross_pay,
        'epf': payslip.epf,
        'esi': payslip.esi,
        'income_tax': payslip.income_tax,
        'canteen': payslip.canteen,
        'society': payslip.society,
        'advance': payslip.advance,
        'insurance': payslip.insurance,
        'total_deduction': payslip.total_deductions,
        'net_pay': payslip.net_pay,
        'Pother': payslip.Pother,
        'Dother': payslip.Dother,
    }

    context = {
        'month': monthName ,
        'year': monthNumy,
        'employment_type': staff.employment_type,
        'page_title': 'PaySlip for ' + monthName,
        'payroll_info': payroll_info,
        'logo': logo,
    }

    template = "main_app/view_payslip_contract.html" if staff.employment_type != 'Regular' else "main_app/view_payslip.html"
    return render(request, template, context)

@login_required
def list_payslip(request, employment_type=None):
    """
    Display a list of payslips with improved filtering logic.
    Shows payslips from the last 6 months by default, with option to show all.
    """
    # Get filter parameters from request
    show_all = request.GET.get('show_all', 'false').lower() == 'true'
    months_back = int(request.GET.get('months_back', 6))

    if show_all:
        # Show all payslips
        payslip_queryset = Payslip.objects.all().order_by('-month')
    else:
        # Show payslips from the last N months (default 6 months)
        # Calculate the first day of the month N months ago
        today = datetime.now().date()
        first_day_current_month = today.replace(day=1)

        # Calculate months back more accurately
        year = first_day_current_month.year
        month = first_day_current_month.month

        for _ in range(months_back):
            month -= 1
            if month < 1:
                month = 12
                year -= 1

        filter_date = datetime(year, month, 1).date()
        payslip_queryset = Payslip.objects.filter(month__gte=filter_date).order_by('-month')

    if employment_type:
        staffs = Staff.objects.filter(employment_type=employment_type).prefetch_related(
            Prefetch('payslip_set', queryset=payslip_queryset, to_attr='payslip_history')
        )
    else:
        staffs = Staff.objects.prefetch_related(
            Prefetch('payslip_set', queryset=payslip_queryset, to_attr='payslip_history')
        )

    # Count total payslips for debugging
    total_payslips = payslip_queryset.count()

    context = {
        'staffs': staffs,
        'page_title': 'Payslip List',
        'employment_type': employment_type,
        'show_all': show_all,
        'months_back': months_back,
        'total_payslips': total_payslips,
    }

    return render(request, "main_app/list_payslip.html", context)

def send_payroll_email(request, staff_id, month):
    try:
        staff = get_object_or_404(Staff, id=staff_id)
        payslip = Payslip.objects.filter(staff=staff, month=month).first()

        if not payslip:
            return JsonResponse({'error': 'Payslip not found'}, status=404)

        if staff.division.name == "NITRA":
            logo= "nitra"
        else:
            logo= "ntc"

        month_year = payslip.month
        monthNum = month_year.month
        monthName = month_name[monthNum]
        num_days_in_month = monthrange(month_year.year, month_year.month)[1]

        payroll_info = {
            'month':   payslip.month.strftime('%B %Y'),
            'employee_code': staff.emp_code,
            'employee_name': staff.user.get_full_name(),
            'employment_type': staff.employment_type,
            'designation': staff.designation.name,
            'department': staff.department.name,
            'father_husband_name': staff.user.father_name,
            'uan_pf_ac': staff.uan,
            'basic': payslip.basic,
            'doj': staff.emp_doj,
            'paid_days': payslip.paid_days,
            'lop': payslip.lop,
            'leave': num_days_in_month - payslip.paid_days + payslip.lop,
            'dp': payslip.dp,
            'da': payslip.da,
            'basic_dp': payslip.basic + payslip.dp,
            'basic_dp_da': payslip.basic + payslip.da + payslip.dp,
            'hra': payslip.hra,
            'conv': payslip.conv,
            'cca': payslip.cca,
            'adhoc': payslip.adhoc,
            'medical': payslip.medical,
            'arrears': payslip.arrears,
            'gross_pay': payslip.gross_pay,
            'epf': payslip.epf,
            'esi': payslip.esi,
            'income_tax': payslip.income_tax,
            'canteen': payslip.canteen,
            'society': payslip.society,
            'advance': payslip.advance,
            'insurance': payslip.insurance,
            'total_deduction': payslip.total_deductions,
            'net_pay': payslip.net_pay,
            'Pother': payslip.Pother,
            'Dother': payslip.Dother,
        }

        if (staff.employment_type != 'Regular' ):
            template_path = "main_app/mail_contract.html"

        else:
            template_path = "main_app/mail.html"

        # Render HTML template to a string

        html = render_to_string(template_path, {'payroll_info': payroll_info, 'logo': logo})

        # Create the PDF using WeasyPrint
        pdf_file = HTML(string=html, base_url=request.build_absolute_uri('/')).write_pdf(stylesheets=[CSS(string='@page { size: A4; margin: 0cm; }')])

        # Create the email message
        email_subject = f"Pay Details for {payroll_info['employee_name']} - Salary for the Month:  {payslip.month.strftime('%B %Y')}"
        email_body = "Please find your attached payslip."
        try:
            email = EmailMessage(email_subject, email_body, settings.EMAIL_HOST_USER, [staff.user.email])
            email.attach(f'PaySlip-{payroll_info["employee_name"]}.pdf', pdf_file, 'application/pdf')
            email.send()

            return JsonResponse({'success': True, 'message': 'Email sent successfully!'})

        except BadHeaderError:
            return JsonResponse({'success': False, 'error': 'Invalid header found.'}, status=400)  # BadHeaderError for invalid email headers
        except SMTPException as e:  # Catch more specific SMTP errors (e.g., address not found)
            return JsonResponse({'success': False, 'error': f'Error sending email: {e}'})
    except Staff.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Staff member not found'}, status=404)
    except Payslip.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Payslip not found'}, status=404)

def send_bulk_email(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            selected_staff_data = data.get('staffs', [])

            for staff_data in selected_staff_data:
                staff_id = staff_data.get('staffId')
                try:
                    staff_id = int(staff_id.split("-")[0])
                except (ValueError, IndexError):
                    messages.error(request, f'Invalid staff ID format: {staff_id}')
                    continue

                month_str = staff_data.get('month')

                try:
                    staff = get_object_or_404(Staff, id=staff_id)
                    month_date = datetime.strptime(month_str, '%Y-%m-%d').date()
                    num_days_in_month = monthrange(month_date.year, month_date.month)[1]

                    if staff.division.name == "NITRA":
                        logo= "nitra"
                    else:
                        logo= "ntc"

                except (ValueError, Staff.DoesNotExist):
                    messages.error(request, f'Invalid month or staff ID: {staff_id}, {month_str}')
                    continue

                payslip = Payslip.objects.filter(staff=staff, month=month_date).first()
                if not payslip:
                    messages.error(request, f'Payslip not found for staff ID {staff_id} and month {month_str}.')
                    continue

                # Prepare payroll_info dictionary (same as before)
                payroll_info = {
                'month':   payslip.month.strftime('%B %Y'),
                'employee_code': staff.emp_code,
                'employee_name': staff.user.get_full_name(),
                'employment_type': staff.employment_type,
                'designation': staff.designation.name,
                'department': staff.department.name,
                'father_husband_name': staff.user.father_name,
                'uan_pf_ac': staff.uan,
                'basic': payslip.basic,
                'doj': staff.emp_doj,
                'paid_days': payslip.paid_days,
                'lop': payslip.lop,
                'dp': payslip.dp,
                'leave': num_days_in_month - payslip.paid_days + payslip.lop,
                'basic_dp': payslip.basic,
                'basic_dp_da': payslip.basic + payslip.da + payslip.dp,
                'hra': payslip.hra,
                'conv': payslip.conv,
                'cca': payslip.cca,
                'adhoc': payslip.adhoc,
                'medical': payslip.medical,
                'arrears': payslip.arrears,
                'gross_pay': payslip.gross_pay,
                'epf': payslip.epf,
                'esi': payslip.esi,
                'income_tax': payslip.income_tax,
                'canteen': payslip.canteen,
                'society': payslip.society,
                'advance': payslip.advance,
                'insurance': payslip.insurance,
                'total_deduction': payslip.total_deductions,
                'net_pay': payslip.net_pay,
            }

                if (staff.employment_type != 'Regular' ):
                    template_path = "main_app/mail_contract.html"

                else:
                    template_path = "main_app/mail.html"

                html = render_to_string(template_path, {'payroll_info': payroll_info, 'logo': logo})

                # Create the PDF using WeasyPrint
                pdf_file = HTML(string=html, base_url=request.build_absolute_uri('/')).write_pdf(stylesheets=[CSS(string='@page { size: A4; margin: 0cm; }')])

                # Create the email message
                email_subject = f"Pay Details for {payroll_info['employee_name']} - Salary for the Month: {payslip.month.strftime('%B %Y')}"

                email_body = "Please find your attached payslip."

                try:
                    email = EmailMessage(email_subject, email_body, settings.EMAIL_HOST_USER, [staff.user.email])
                    email.attach(f'PaySlip-{payroll_info["employee_name"]}.pdf', pdf_file, 'application/pdf')
                    email.send()

                    return JsonResponse({'success': True, 'message': 'Email sent successfully!'})

                except BadHeaderError:
                    return JsonResponse({'success': False, 'error': 'Invalid header found.'}, status=400)  # BadHeaderError for invalid email headers
                except SMTPException as e:  # Catch more specific SMTP errors (e.g., address not found)
                    return JsonResponse({'success': False, 'error': f'Error sending email: {e}'})  # Include the error details in the response

            return JsonResponse({'success': True, 'message': 'Emails sent successfully!'})

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid data format.'}, status=400)

    else:
        return JsonResponse({'error': 'Invalid request method'}, status=405)


def custom_404(request, exception):
    """
    Custom 404 error handler.
    """
    return render(request, '404.html', status=404)

def custom_500(request):
    """
    Custom 500 error handler.
    """
    return render(request, '500.html', status=500)

@login_required
@require_POST
def save_fixed(request):
    if request.method == 'POST':
        id = request.POST.get('fixed')
        month_year_str = request.POST.get('month')
        hra = request.POST.get('hra')
        da = request.POST.get('da')
        division = request.POST.get('division')

        # Enhanced validation
        errors = {}

        # Validate month
        if not month_year_str:
            errors['month'] = 'Month is required'
        else:
            try:
                month_year = datetime.strptime(month_year_str + '-01', '%Y-%m-%d')
                month = month_year.date()
            except ValueError:
                errors['month'] = 'Invalid month format. Please use YYYY-MM.'

        # Validate DA
        if not da:
            errors['da'] = 'DA percentage is required'
        else:
            try:
                da_float = float(da)
                if da_float < 0 or da_float > 100:
                    errors['da'] = 'DA percentage must be between 0 and 100'
            except ValueError:
                errors['da'] = 'DA percentage must be a valid number'

        # Validate HRA
        if not hra:
            errors['hra'] = 'HRA percentage is required'
        else:
            try:
                hra_float = float(hra)
                if hra_float < 0 or hra_float > 100:
                    errors['hra'] = 'HRA percentage must be between 0 and 100'
            except ValueError:
                errors['hra'] = 'HRA percentage must be a valid number'

        # Validate division
        if not division:
            errors['division'] = 'Division is required'

        if errors:
            return JsonResponse({'success': False, 'errors': errors})

        try:
            division_id = Division.objects.get(name=division)

            fixed, created = Fixed.objects.update_or_create(
                division=division_id,
                defaults={'hra': hra, 'da': da, 'month': month}
            )

            if created:
                return JsonResponse({'success': True, 'message': 'Fixed allowance details saved successfully'})
            else:
                return JsonResponse({'success': True, 'message': 'Fixed allowance details updated successfully'})

        except Division.DoesNotExist:
            return JsonResponse({'success': False, 'errors': {'division': 'Invalid division selected'}})
        except Exception as e:
            return JsonResponse({'success': False, 'errors': {'general': f"Error saving fixed allowances: {str(e)}"}})
    else:
        return JsonResponse({'success': False, 'message': 'Invalid request method'})

@login_required
@require_POST
def save_deduction_details(request):
    if request.method == 'POST':
        staff_id = request.POST.get('staff')
        society = request.POST.get('society')
        income_tax = request.POST.get('income_tax')
        canteen = request.POST.get('canteen')
        advance = request.POST.get('advance')
        insurance = request.POST.get('insurance')
        other = request.POST.get('other')

        # Validate staff_id
        if not staff_id:
            return JsonResponse({'success': False, 'message': 'Staff ID is required'}, status=400)

        try:
            # Convert to int if it's a string
            if isinstance(staff_id, str):
                staff_id = int(staff_id)

            staff = Staff.objects.get(id=staff_id)

            deductions, created = Deductions.objects.get_or_create(staff=staff)
            deductions.society = society or 0
            deductions.income_tax = income_tax or 0
            deductions.canteen = canteen or 0
            deductions.advance = advance or 0
            deductions.insurance = insurance or 0
            deductions.other = other or 0
            deductions.save()

            if created:
                return JsonResponse({'success': True, 'message': 'Deduction details saved successfully'})
            else:
                return JsonResponse({'success': True, 'message': 'Deduction details updated successfully'})

        except ValueError:
            return JsonResponse({'success': False, 'message': f'Invalid staff ID format: {staff_id}'}, status=400)
        except Staff.DoesNotExist:
            return JsonResponse({'success': False, 'message': f'Staff member with ID {staff_id} not found'}, status=404)
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)}, status=400)
    return JsonResponse({'success': False, 'message': 'Invalid request'}, status=400)

@login_required
@require_POST
def save_attendence_details(request):
    if request.method == 'POST':
        staff_id = request.POST.get('staff')
        lop = request.POST.get('lop')
        paid_days = request.POST.get('paid_days')

        try:
            staff = Staff.objects.get(id=staff_id)
            attendance, created = Attendance.objects.get_or_create(staff=staff)
            attendance.paid_days = paid_days
            attendance.lop = lop
            attendance.save()

            if created:
                return JsonResponse({'success': True, 'message': 'Attendance details saved successfully'})
            else:
                return JsonResponse({'success': True, 'message': 'Attendance details updated successfully'})

        except Staff.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Staff member not found'}, status=404)
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)}, status=400)
    return JsonResponse({'success': False, 'message': 'Invalid request'}, status=400)

@login_required
@require_POST
def save_contract_pay(request):
    if request.method == 'POST':
        month_year_str = request.POST.get('month')
        staff_id = request.POST.get('staff')
        hra = request.POST.get('hra')
        adhoc = request.POST.get('adhoc')
        arrears = request.POST.get('arrears')
        other = request.POST.get('other')

        # Enhanced validation
        errors = {}

        # Validate staff
        if not staff_id:
            errors['staff'] = 'Staff ID is required'

        # Validate month
        if not month_year_str:
            errors['month'] = 'Month is required'
        else:
            try:
                month_year = datetime.strptime(month_year_str + '-01', '%Y-%m-%d')
                month = month_year.date()
            except ValueError:
                errors['month'] = 'Invalid month format. Please use YYYY-MM.'

        # Validate HRA
        if hra is not None and hra != '':
            try:
                hra_float = float(hra)
                if hra_float < 0:
                    errors['hra'] = 'HRA amount cannot be negative'
            except ValueError:
                errors['hra'] = 'HRA must be a valid number'
        else:
            hra = 0

        # Validate adhoc
        if adhoc is not None and adhoc != '':
            try:
                adhoc_float = float(adhoc)
                if adhoc_float < 0:
                    errors['adhoc'] = 'Adhoc amount cannot be negative'
            except ValueError:
                errors['adhoc'] = 'Adhoc must be a valid number'
        else:
            adhoc = 0

        # Validate arrears
        if arrears is not None and arrears != '':
            try:
                arrears_float = float(arrears)
                if arrears_float < 0:
                    errors['arrears'] = 'Arrears amount cannot be negative'
            except ValueError:
                errors['arrears'] = 'Arrears must be a valid number'
        else:
            arrears = 0

        # Validate other
        if other is not None and other != '':
            try:
                other_float = float(other)
                if other_float < 0:
                    errors['other'] = 'Other pay amount cannot be negative'
            except ValueError:
                errors['other'] = 'Other pay must be a valid number'
        else:
            other = 0

        if errors:
            return JsonResponse({'success': False, 'errors': errors})

        try:
            staff = Staff.objects.get(id=staff_id)
            contract_pay, created = ContractPay.objects.get_or_create(staff=staff)
            contract_pay.staff = staff
            contract_pay.hra = hra
            contract_pay.adhoc = adhoc
            contract_pay.month = month
            contract_pay.arrears = arrears
            contract_pay.other = other
            contract_pay.save()

            message = 'Contract pay details saved successfully' if created else 'Contract pay details updated successfully'
            return JsonResponse({'success': True, 'message': message})

        except Staff.DoesNotExist:
            return JsonResponse({'success': False, 'errors': {'staff': 'Staff member not found'}})
        except Exception as e:
            return JsonResponse({'success': False, 'errors': {'general': f"Error saving contract pay: {str(e)}"}})
    return JsonResponse({'success': False, 'message': 'Invalid request method'})

@login_required
@require_POST
def save_regular_pay(request):
    if request.method == 'POST':
        staff_id = request.POST.get('staff')
        arrears = request.POST.get('arrears')
        other = request.POST.get('other')

        # Enhanced validation
        errors = {}

        # Validate staff
        if not staff_id:
            errors['staff'] = 'Staff ID is required'

        # Validate arrears
        if arrears is not None and arrears != '':
            try:
                arrears_float = float(arrears)
                if arrears_float < 0:
                    errors['arrears'] = 'Arrears amount cannot be negative'
            except ValueError:
                errors['arrears'] = 'Arrears must be a valid number'
        else:
            arrears = 0

        # Validate other pay
        if other is not None and other != '':
            try:
                other_float = float(other)
                if other_float < 0:
                    errors['other'] = 'Other pay amount cannot be negative'
            except ValueError:
                errors['other'] = 'Other pay must be a valid number'
        else:
            other = 0

        if errors:
            return JsonResponse({'success': False, 'errors': errors})

        try:
            staff = Staff.objects.get(id=staff_id)
            regular_pay, created = RegularPay.objects.get_or_create(staff=staff)
            regular_pay.staff = staff
            regular_pay.arrears = arrears
            regular_pay.other = other
            regular_pay.save()

            message = 'Regular pay details saved successfully' if created else 'Regular pay details updated successfully'
            return JsonResponse({'success': True, 'message': message})

        except Staff.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Staff member not found'}, status=404)
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)}, status=400)
    return JsonResponse({'success': False, 'message': 'Invalid request'}, status=400)
